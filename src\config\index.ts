// 全局配置
export const GLOBAL_CONFIG = {
  // Wechaty配置
  wechaty: {
    token: 'puppet_workpro_5ab9612aa43d46b9965e5da9bb87b1d6',
    puppet: '@juzi/wechaty-puppet-service',
    tls: {
      disable: true
    }
  },

  // MySQL配置
  mysql: {
    host: process.env.MYSQL_HOST || '127.0.0.1',
    port: parseInt(process.env.MYSQL_PORT || '3306'),
    user: process.env.MYSQL_USER || 'root',
    password: process.env.MYSQL_PASSWORD || 'root',
    database: process.env.MYSQL_DB || 'wbot',
    connectionLimit: parseInt(process.env.MYSQL_POOL || '10')
  },

  // 同济接口配置
  tongjiApi: {
    USER_PERSON_INFO_BY_PID_URL: 'https://api.tongji.edu.cn/v2/dc/user/person_info_by_pid', // 根据人员唯一编号pid查询人员所有学工号基本信息
    OAUTH2_TOKEN_URL: 'https://***************/oauth2/token', // OAuth2 token获取接口
    CLIENT_ID: 'test_test_test',
    CLIENT_SECRET: '4yq1l86FG9YZxG2EGa728pS1SGJYepK1',
    timeout: 10000
  },

  // 验证码配置
  verifyCode: {
    timeout: 60000, // 60秒超时
    port: 3000
  },

  // 欢迎消息配置
  welcome: {
    message: (name: string, roomName?: string) => `欢迎 @${name} 加入${roomName ? roomName : '群聊'}！我是同济智能助手，发送"帮助"了解我能做什么～`
  },

  // 关键词回复配置
  keywords: [
    {
      keyword: /帮助|help/i,
      reply: ({ room }: any) => {
        if (room) {
          return `嗨，我是同济智能助手！在群聊中请 @我 提问或发送关键字～ 😊`
        }
        return `嗨，我是同济智能助手，发送关键字或直接提问～ 😊`
      }
    },
    {
      keyword: /结束对话|停止对话|bye|再见|结束|停止/i,
      reply: ({ room, talker }: any) => {
        if (room) {
          return `@${talker.name()} 好的，对话已结束。如需重新开始，请 @我 提问～`
        }
        return '好的，对话已结束。如需重新开始，请直接提问～'
      }
    }
  ]
}
