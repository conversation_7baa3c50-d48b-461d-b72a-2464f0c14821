// 存储用户的会话状态
const conversationStates = new Map();
// 结束会话的关键词
const END_SESSION_KEYWORDS = ['结束对话', '停止对话', 'bye', '再见', '结束', '停止', '不聊了', '结束聊天', '停止聊天', '退出对话'];
// 检查是否包含结束会话的关键词
export function containsEndSessionKeyword(text) {
    const lowerText = text.toLowerCase();
    return END_SESSION_KEYWORDS.some((keyword) => lowerText.includes(keyword.toLowerCase()));
}
// 检查是否在与其他群成员对话
export function isTalkingToOthers(text, roomMembers) {
    const mentionPattern = /@([^@\s]+)/g;
    const mentions = text.match(mentionPattern);
    if (mentions) {
        return mentions.some((mention) => {
            const mentionedName = mention.replace('@', '').trim();
            return roomMembers.some((member) => member.includes(mentionedName) || mentionedName.includes(member));
        });
    }
    return false;
}
// 获取或创建会话状态
export function getConversationState(sessionKey) {
    if (!conversationStates.has(sessionKey)) {
        conversationStates.set(sessionKey, {
            isActive: false,
            lastActivity: new Date(),
            endKeywords: [...END_SESSION_KEYWORDS]
        });
    }
    return conversationStates.get(sessionKey);
}
// 激活会话
export function activateConversation(sessionKey) {
    const state = getConversationState(sessionKey);
    state.isActive = true;
    state.lastActivity = new Date();
}
// 结束会话
export function endConversation(sessionKey) {
    const state = getConversationState(sessionKey);
    state.isActive = false;
    state.lastActivity = new Date();
}
// 检查会话是否活跃（5分钟内且有活动状态）
export function isConversationActive(sessionKey) {
    const state = getConversationState(sessionKey);
    const now = new Date();
    const timeDiff = now.getTime() - state.lastActivity.getTime();
    return state.isActive && timeDiff < 5 * 60 * 1000; // 5分钟
}
// 更新最后活动时间
export function updateLastActivity(sessionKey) {
    const state = getConversationState(sessionKey);
    state.lastActivity = new Date();
}
// 清理过期的会话状态（超过1小时）
export function cleanupExpiredStates() {
    const now = new Date();
    const oneHour = 60 * 60 * 1000;
    for (const [sessionKey, state] of conversationStates.entries()) {
        const timeDiff = now.getTime() - state.lastActivity.getTime();
        if (timeDiff > oneHour) {
            conversationStates.delete(sessionKey);
        }
    }
}
// 定期清理过期状态（每30分钟执行一次）
setInterval(cleanupExpiredStates, 30 * 60 * 1000);
//# sourceMappingURL=conversationState.js.map