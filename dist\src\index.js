import { ScanStatus, WechatyBuilder, types } from '@juzi/wechaty';
import QrcodeTerminal from 'qrcode-terminal';
import { GLOBAL_CONFIG } from './config/index.js';
import { getVerifyCode } from './utils/verifyCode.js';
import { handleGroupMessage, handlePrivateMessage } from './handlers/messageHandler.js';
// 存储二维码key
const store = {
    qrcodeKey: ''
};
// 创建机器人实例
const bot = WechatyBuilder.build({
    puppet: GLOBAL_CONFIG.wechaty.puppet,
    puppetOptions: {
        token: GLOBAL_CONFIG.wechaty.token,
        tls: GLOBAL_CONFIG.wechaty.tls
    }
});
// 二维码扫描处理
let hasShownQrcode = false;
bot
    .on('scan', (qrcode, status, data) => {
    switch (status) {
        case ScanStatus.Waiting:
            if (!hasShownQrcode) {
                store.qrcodeKey = getQrcodeKey(qrcode) || '';
                QrcodeTerminal.generate(qrcode, { small: true });
                console.log('\n请使用企业微信扫描上方二维码登录\n');
                hasShownQrcode = true;
            }
            break;
        case ScanStatus.Scanned:
            console.log('\n用户已扫描二维码，请确认登录\n');
            break;
        case ScanStatus.Confirmed:
            console.log('\n用户已确认登录\n');
            break;
        case ScanStatus.Timeout:
            console.log('\n用户已取消登录\n');
            hasShownQrcode = false;
            break;
        default:
            console.log(`扫码状态: ${status}, data: ${data}`);
            break;
    }
})
    .on('verify-code', async (id, message, scene, status) => {
    if (status === types.VerifyCodeStatus.WAITING && scene === types.VerifyCodeScene.LOGIN && id === store.qrcodeKey) {
        console.log(`receive verify-code event, id: ${id}, message: ${message}, scene: ${types.VerifyCodeScene[scene]} status: ${types.VerifyCodeStatus[status]}`);
        const verifyCode = await getVerifyCode();
        try {
            await bot.enterVerifyCode(id, verifyCode);
            return;
        }
        catch (e) {
            console.log(e.message);
            await bot.refreshQrCode();
        }
    }
})
    .on('login', (user) => {
    console.log(`\n  ============================================
  user: ${JSON.stringify(user)}, friend: ${user.friend()}, ${user.coworker()}
  ============================================\n`);
})
    // .on('room-join', async (room: Room, inviteeList: Contact[], inviter: Contact) => {
    //   try {
    //     const topic = await room.topic()
    //     for (const invitee of inviteeList) {
    //       const name = invitee.name()
    //       await room.say(GLOBAL_CONFIG.welcome.message(name, topic), invitee)
    //     }
    //   } catch (e) {
    //     console.error('发送欢迎消息失败:', e)
    //   }
    // })
    .on('message', async (message) => {
    const room = message.room();
    // const talker = message.talker()
    if (room) {
        // 群聊
        await handleGroupMessage(message);
    }
    else {
        // 私聊
        await handlePrivateMessage(message);
    }
})
    .on('error', (err) => {
    console.log(err);
})
    .on('ready', async () => {
    const contactList = await bot.Wecom.wechaty.Contact.findAll();
    console.info('Total number of contacts:', contactList.length);
    for (const contact of contactList) {
        console.info(contact);
    }
})
    .on('friendship', async (friendship) => {
    let logMsg;
    try {
        logMsg = 'received `friend` event from ' + friendship.contact().name();
        console.log(logMsg);
        switch (friendship.type()) {
            case types.Friendship.Receive:
                logMsg = 'accepted automatically because verify message is "ding"';
                console.log('before accept');
                await friendship.accept();
                await new Promise((r) => setTimeout(r, 1000));
                await friendship.contact().say('嗨，我是同济智能助手，发送关键字或 @我 提问～ 😊');
                console.log('after accept');
                break;
            case types.Friendship.Confirm:
                logMsg = 'friendship confirmed with ' + friendship.contact().name();
                break;
            default:
                break;
        }
    }
    catch (e) {
        logMsg = e.message;
    }
    console.log(logMsg);
});
// 启动机器人
console.log('启动机器人...');
bot.start();
// 工具函数：从二维码URL中提取key
const getQrcodeKey = (urlStr) => {
    const str_begin = 'key=';
    const str_end = '&';
    const index_begin = urlStr.indexOf(str_begin);
    const index_end = urlStr.indexOf(str_end);
    const str_key = urlStr.slice(index_begin + 4, index_end);
    console.log(str_key);
    return str_key;
};
//# sourceMappingURL=index.js.map