import { GLOBAL_CONFIG } from '../config/index.js';
import { tongjiApiService } from './tongjiApi.js';
import { containsEndSessionKeyword, endConversation } from './conversationState.js';
import { getSessionKey } from '../agents/utils/session.js';
import { logAssistantText, logUserMessage } from '../services/conversationLog.js';
// 关键词处理
export async function checkAndHandleKeyword(room, talker, text, isMentionMe) {
    if (!text)
        return false;
    // 对于群聊，只处理@机器人的消息
    if (room && !isMentionMe) {
        return false;
    }
    // 检查是否是结束会话关键词
    if (containsEndSessionKeyword(text)) {
        const sessionKey = getSessionKey(room, talker);
        // 记录用户消息
        if (room) {
            await logUserMessage({
                room: () => room,
                talker: () => talker,
                text: () => text,
                type: () => 1
            });
        }
        else {
            await logUserMessage({
                room: () => null,
                talker: () => talker,
                text: () => text,
                type: () => 1
            });
        }
        // 发送确认消息
        const content = typeof GLOBAL_CONFIG.keywords[1].reply === 'function' ? GLOBAL_CONFIG.keywords[1].reply({ talker, room, text }) : GLOBAL_CONFIG.keywords[1].reply;
        if (room) {
            await room.say(content);
        }
        else {
            await talker.say(content);
        }
        // 记录机器人回复
        await logAssistantText(room, talker, content, 'session_end_confirmation');
        // 结束会话
        endConversation(sessionKey);
        return true;
    }
    const tongjiResult = await tongjiApiService.handleTongjiQuery(text, talker);
    if (tongjiResult) {
        // 记录用户消息
        if (room) {
            await logUserMessage({
                room: () => room,
                talker: () => talker,
                text: () => text,
                type: () => 1
            });
        }
        else {
            await logUserMessage({
                room: () => null,
                talker: () => talker,
                text: () => text,
                type: () => 1
            });
        }
        // 发送查询结果
        if (room) {
            await room.say(`@${talker.name()} ${tongjiResult}`);
        }
        else {
            await talker.say(tongjiResult);
        }
        // 记录机器人回复
        await logAssistantText(room, talker, tongjiResult, 'tongji_api_query');
        return true;
    }
    for (const item of GLOBAL_CONFIG.keywords) {
        const matched = typeof item.keyword === 'string' ? text.includes(item.keyword) : item.keyword.test(text);
        if (matched) {
            const content = typeof item.reply === 'function' ? item.reply({ talker, room, text }) : item.reply;
            if (room) {
                await room.say(content);
            }
            else {
                await talker.say(content);
            }
            return true;
        }
    }
    return false;
}
//# sourceMappingURL=keywords.js.map