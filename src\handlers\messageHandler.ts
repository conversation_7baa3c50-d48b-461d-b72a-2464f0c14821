import { Message, Room, Contact, types } from '@juzi/wechaty'
import { getAgent } from '../agents/registry.js'
import { getCurrentAgentId, getSessionKey } from '../agents/utils/session.js'
import { parseAgentCommand } from '../utils/commands.js'
import { checkAndHandleKeyword } from '../utils/keywords.js'
import { PermissionManager } from '../utils/permissions.js'
import { logUserMessage, logAssistantText } from '../services/conversationLog.js'
import { insertConversationRecord } from '../dao/conversationDao.js'
import { getGroupConfig, upsertGroupConfig } from '../dao/groupConfigDao.js'
import { activateConversation, endConversation } from '../utils/conversationState.js'

// AI 对话处理
export async function handleAIResponse(room: Room, talker: Contact, mentionText: string): Promise<void> {
  console.log('进入AI对话处理函数...')
  console.log('询问内容：', mentionText)

  const sessionKey = getSessionKey(room, talker)

  // 激活会话状态
  activateConversation(sessionKey)

  const agentId = await getCurrentAgentId(sessionKey, room)
  let agent
  try {
    agent = await getAgent(agentId)
  } catch (error) {
    console.log(`智能体 ${agentId} 不存在，使用默认智能体 tjtx`)
    agent = await getAgent('tjtx')
  }
  if (!agent) {
    console.error('未找到对应的AI代理')
    await room.say(`@${talker.name()} 抱歉，未找到代理，请联系管理员`)
    return
  }

  try {
    const reply = await agent.respond(mentionText, { room, talker, message: null as any })
    if (typeof reply === 'string' && reply.trim()) {
      await logAssistantText(room, talker, reply)
    }
  } catch (error) {
    console.error('AI对话处理错误:', error)
    await room.say(`@${talker.name()} 抱歉，处理对话时发生错误`)
  }
}

// 处理群聊消息
export async function handleGroupMessage(message: Message): Promise<void> {
  const room = message.room()
  const talker = message.talker()
  const mentionText = await message.mentionText()
  const text = message.text()
  const messageType = message.type()

  if (!room) return

  // 多媒体消息
  if (messageType === types.Message.Image) {
    await handleGroupImage(message)
    return
  } else if (messageType === types.Message.Attachment) {
    await handleGroupFile(message)
    return
  } else if (messageType === types.Message.Audio) {
    await handleGroupAudio(message)
    return
  } else if (messageType === types.Message.Video) {
    await handleGroupVideo(message)
    return
  }

  // 记录用户消息（仅@机器人的文本消息）
  const isMentionMe = await message.mentionSelf()
  if (messageType === types.Message.Text && text && text.trim() && isMentionMe) {
    await logUserMessage(message)
  }

  // 群聊处理：只处理@机器人的消息
  if (messageType === types.Message.Text && text && text.trim()) {
    // 群聊中只处理@机器人的消息
    if (!isMentionMe) {
      console.log('群聊消息未@机器人，跳过处理')
      return
    }

    // 智能体命令（只有@机器人时才处理）
    // 使用 mentionText 而不是 text 来解析指令，因为 text 包含@机器人部分
    const cmd = parseAgentCommand(mentionText)
    const sessionKey = getSessionKey(room, talker)

    if (cmd.cmd === 'set' && cmd.arg) {
      // 检查权限：只有群主或管理员可以使用agent命令
      const hasPermission = await PermissionManager.canExecuteAgentCommand(room, talker)
      if (!hasPermission) {
        await room.say(`@${talker.name()} 抱歉，只有群主或管理员可以使用智能体命令`)
        return
      }

      const agent = await getAgent(cmd.arg)
      if (agent) {
        // 允许切换需检查配置
        const cfg = await getGroupConfig(room.id)
        if (cfg && cfg.allow_agent_switch === 0) {
          await room.say(`@${talker.name()} 当前群不允许切换智能体`)
          return
        }
        await upsertGroupConfig({ room_id: room.id, agent_id: cmd.arg })

        endConversation(sessionKey)

        await room.say(`已将当前智能体设置为：${cmd.arg}`)
      } else {
        const { listAgents } = await import('../agents/registry')
        const agentsList = await listAgents()
        await room.say(`未找到智能体：${cmd.arg}\n可用列表：\n${agentsList}`)
      }
      return
    } else if (cmd.cmd === 'list') {
      // 检查权限
      const hasPermission = await PermissionManager.canExecuteAgentCommand(room, talker)
      if (!hasPermission) {
        await room.say(`@${talker.name()} 抱歉，只有群主或管理员可以使用智能体命令`)
        return
      }

      const { listAgents } = await import('../agents/registry')
      const agentsList = await listAgents()
      await room.say(`可用智能体列表：\n${agentsList}`)
      return
    } else if (cmd.cmd === 'current') {
      // 检查权限
      const hasPermission = await PermissionManager.canExecuteAgentCommand(room, talker)
      if (!hasPermission) {
        await room.say(`@${talker.name()} 抱歉，只有群主或管理员可以使用智能体命令`)
        return
      }

      const currentAgent = await getCurrentAgentId(sessionKey, room)
      await room.say(`当前智能体：${currentAgent}`)
      return
    }

    // 处理关键字和AI回复（只有@机器人时才处理）
    // 使用 mentionText 而不是 text 来处理关键字，保持一致性
    const handled = await checkAndHandleKeyword(room, talker, mentionText.trim(), isMentionMe)
    if (!handled) {
      console.log(`被@的具体内容: ${mentionText}`)
      await handleAIResponse(room, talker, mentionText)
    }
  }
}

// 处理私聊消息
export async function handlePrivateMessage(message: Message): Promise<void> {
  const talker = message.talker()
  const text = message.text()
  const messageType = message.type()

  // 处理多媒体消息
  if (messageType === types.Message.Image) {
    await handlePrivateImage(message)
    return
  } else if (messageType === types.Message.Attachment) {
    await handlePrivateFile(message)
    return
  } else if (messageType === types.Message.Audio) {
    await handlePrivateAudio(message)
    return
  } else if (messageType === types.Message.Video) {
    await handlePrivateVideo(message)
    return
  }

  // 记录用户消息（仅文本）
  if (messageType === types.Message.Text && text && text.trim()) {
    await logUserMessage(message)
  }

  console.log('私聊文本信息:', text)

  if (text === 'help' || text === '帮助') {
    await message.say(`嗨，我是同济智能助手，发送关键字或直接提问～ 😊`)
  } else {
    // 智能体命令（私聊不支持切换，直接忽略命令以避免误触）
    // const cmd = parseAgentCommand(text)
    const sessionKey = getSessionKey(null, talker)

    // if (cmd.cmd === 'list') {
    //   const { listAgents } = await import('../agents/registry')
    //   await message.say(`可用智能体列表：\n${listAgents()}`)
    //   return
    // } else if (cmd.cmd === 'current') {
    //   const currentAgent = await getCurrentAgentId(sessionKey, null)
    //   await message.say(`当前智能体：${currentAgent}`)
    //   return
    // }

    const handled = await checkAndHandleKeyword(null, talker, text.trim(), true)
    if (!handled) {
      // 按当前智能体路由
      const agentId = await getCurrentAgentId(sessionKey, null)
      try {
        let agent
        try {
          agent = await getAgent(agentId)
        } catch (error) {
          console.log(`智能体 ${agentId} 不存在，使用默认智能体 tjtx`)
          agent = await getAgent('tjtx')
        }

        if (agent) {
          const reply = await agent.respond(text, { room: null, talker, message })
          if (typeof reply === 'string' && reply.trim()) {
            await message.say(reply)
            await logAssistantText(null, talker, reply)
          }
        }
      } catch (error) {
        console.error('私聊AI处理错误:', error)
        await message.say('抱歉，处理对话时发生错误')
      }
    }
  }
}

// 私聊多媒体处理函数
async function handlePrivateImage(message: Message): Promise<void> {
  const talker = message.talker()
  console.log('收到私聊图片')

  // 记录图片消息
  await logMultimediaMessage(message, 'image')

  // 智能体反问
  const sessionKey = getSessionKey(null, talker)
  const agentId = await getCurrentAgentId(sessionKey, null)

  let agent
  try {
    agent = await getAgent(agentId)
  } catch (error) {
    console.log(`智能体 ${agentId} 不存在，使用默认智能体 tjtx`)
    agent = await getAgent('tjtx')
  }

  if (agent) {
    try {
      const reply = '我收到了您的图片，想具体了解点什么呢？'
      await message.say(reply)
      await logAssistantText(null, talker, reply)
    } catch (error) {
      console.error('私聊图片处理错误:', error)
    }
  }
}

async function handlePrivateFile(message: Message): Promise<void> {
  const talker = message.talker()
  console.log('收到私聊文件')

  // 记录文件消息
  await logMultimediaMessage(message, 'file')

  // 智能体反问
  const sessionKey = getSessionKey(null, talker)
  const agentId = await getCurrentAgentId(sessionKey, null)

  let agent
  try {
    agent = await getAgent(agentId)
  } catch (error) {
    console.log(`智能体 ${agentId} 不存在，使用默认智能体 tjtx`)
    agent = await getAgent('tjtx')
  }

  if (agent) {
    try {
      const reply = '我收到了您上传的文件，想具体了解点什么呢？'
      await message.say(reply)
      await logAssistantText(null, talker, reply)
    } catch (error) {
      console.error('私聊文件处理错误:', error)
    }
  }
}

async function handlePrivateAudio(message: Message): Promise<void> {
  const talker = message.talker()
  console.log('收到私聊语音')

  // 记录语音消息
  await logMultimediaMessage(message, 'audio')

  // 智能体反问
  const sessionKey = getSessionKey(null, talker)
  const agentId = await getCurrentAgentId(sessionKey, null)

  let agent
  try {
    agent = await getAgent(agentId)
  } catch (error) {
    console.log(`智能体 ${agentId} 不存在，使用默认智能体 tjtx`)
    agent = await getAgent('tjtx')
  }

  if (agent) {
    try {
      const reply = '我收到了您的语音消息，想具体了解点什么呢？'
      await message.say(reply)
      await logAssistantText(null, talker, reply)
    } catch (error) {
      console.error('私聊语音处理错误:', error)
    }
  }
}

async function handlePrivateVideo(message: Message): Promise<void> {
  const talker = message.talker()
  console.log('收到私聊视频')

  // 记录视频消息
  await logMultimediaMessage(message, 'video')

  // 智能体反问
  const sessionKey = getSessionKey(null, talker)
  const agentId = await getCurrentAgentId(sessionKey, null)

  let agent
  try {
    agent = await getAgent(agentId)
  } catch (error) {
    console.log(`智能体 ${agentId} 不存在，使用默认智能体 tjtx`)
    agent = await getAgent('tjtx')
  }

  if (agent) {
    try {
      const reply = '我收到了您的视频，想具体了解点什么呢？'
      await message.say(reply)
      await logAssistantText(null, talker, reply)
    } catch (error) {
      console.error('私聊视频处理错误:', error)
    }
  }
}

// 群聊多媒体处理函数
async function handleGroupImage(message: Message): Promise<void> {
  const room = message.room()
  const talker = message.talker()
  const isMentionMe = await message.mentionSelf()

  if (!room) return

  console.log('收到群聊图片', isMentionMe ? '(被@)' : '')

  const sessionKey = getSessionKey(room, talker)

  // 群聊中只处理@机器人的图片
  if (!isMentionMe) {
    console.log('群聊图片未@机器人，跳过处理')
    return
  }

  // 记录图片消息
  await logMultimediaMessage(message, 'image')

  // 激活会话状态
  activateConversation(sessionKey)

  // 智能体反问
  const agentId = await getCurrentAgentId(sessionKey, room)

  let agent
  try {
    agent = await getAgent(agentId)
  } catch (error) {
    console.log(`智能体 ${agentId} 不存在，使用默认智能体 tjtx`)
    agent = await getAgent('tjtx')
  }

  if (agent) {
    try {
      const reply = `@${talker.name()} 我收到了您的图片，想具体了解点什么呢？`
      await room.say(reply)
      await logAssistantText(room, talker, reply)
    } catch (error) {
      console.error('群聊图片处理错误:', error)
    }
  }
}

async function handleGroupFile(message: Message): Promise<void> {
  const room = message.room()
  const talker = message.talker()
  const isMentionMe = await message.mentionSelf()

  if (!room) return

  console.log('收到群聊文件', isMentionMe ? '(被@)' : '')

  const sessionKey = getSessionKey(room, talker)

  // 群聊中只处理@机器人的文件
  if (!isMentionMe) {
    console.log('群聊文件未@机器人，跳过处理')
    return
  }

  // 记录文件消息
  await logMultimediaMessage(message, 'file')

  // 激活会话状态
  activateConversation(sessionKey)

  // 智能体反问
  const agentId = await getCurrentAgentId(sessionKey, room)

  let agent
  try {
    agent = await getAgent(agentId)
  } catch (error) {
    console.log(`智能体 ${agentId} 不存在，使用默认智能体 tjtx`)
    agent = await getAgent('tjtx')
  }

  if (agent) {
    try {
      const reply = `@${talker.name()} 我收到了您的文件，想具体了解点什么呢？`
      await room.say(reply)
      await logAssistantText(room, talker, reply)
    } catch (error) {
      console.error('群聊文件处理错误:', error)
    }
  }
}

async function handleGroupAudio(message: Message): Promise<void> {
  const room = message.room()
  const talker = message.talker()
  const isMentionMe = await message.mentionSelf()

  if (!room) return

  console.log('收到群聊语音', isMentionMe ? '(被@)' : '')

  const sessionKey = getSessionKey(room, talker)

  // 群聊中只处理@机器人的语音
  if (!isMentionMe) {
    console.log('群聊语音未@机器人，跳过处理')
    return
  }

  // 记录语音消息
  await logMultimediaMessage(message, 'audio')

  // 激活会话状态
  activateConversation(sessionKey)

  // 智能体反问
  const agentId = await getCurrentAgentId(sessionKey, room)

  let agent
  try {
    agent = await getAgent(agentId)
  } catch (error) {
    console.log(`智能体 ${agentId} 不存在，使用默认智能体 tjtx`)
    agent = await getAgent('tjtx')
  }

  if (agent) {
    try {
      const reply = `@${talker.name()} 我收到了您的语音消息，想具体了解点什么呢？`
      await room.say(reply)
      await logAssistantText(room, talker, reply)
    } catch (error) {
      console.error('群聊语音处理错误:', error)
    }
  }
}

async function handleGroupVideo(message: Message): Promise<void> {
  const room = message.room()
  const talker = message.talker()
  const isMentionMe = await message.mentionSelf()

  if (!room) return

  console.log('收到群聊视频', isMentionMe ? '(被@)' : '')

  const sessionKey = getSessionKey(room, talker)

  // 群聊中只处理@机器人的视频
  if (!isMentionMe) {
    console.log('群聊视频未@机器人，跳过处理')
    return
  }

  // 记录视频消息
  await logMultimediaMessage(message, 'video')

  // 激活会话状态
  activateConversation(sessionKey)

  // 智能体反问
  const agentId = await getCurrentAgentId(sessionKey, room)

  let agent
  try {
    agent = await getAgent(agentId)
  } catch (error) {
    console.log(`智能体 ${agentId} 不存在，使用默认智能体 tjtx`)
    agent = await getAgent('tjtx')
  }

  if (agent) {
    try {
      const reply = `@${talker.name()} 我收到了您的视频，想具体了解点什么呢？`
      await room.say(reply)
      await logAssistantText(room, talker, reply)
    } catch (error) {
      console.error('群聊视频处理错误:', error)
    }
  }
}

// 记录多媒体消息
async function logMultimediaMessage(message: Message, messageType: 'image' | 'audio' | 'video' | 'file'): Promise<void> {
  const room = message.room()
  const talker = message.talker()

  const rec = {
    conversation_id: room ? room.id : talker.id,
    conversation_type: (room ? 'group' : 'private') as any,
    room_id: room ? room.id : null,
    room_name: room ? await room.topic() : null,
    user_id: talker.id,
    user_name: talker.name(),
    role: 'user' as any,
    message_type: messageType as any,
    content: null,
    content_url: null,
    metadata: JSON.stringify({
      originalType: message.type(),
      timestamp: new Date().toISOString()
    }),
    model: null,
    status_code: null,
    duration_ms: null,
    parent_message_id: null,
    trace_id: null,
    ip_address: null,
    device_info: null
  }

  await insertConversationRecord(rec)
}

// 消息类型处理函数
async function handleImageSend(message: Message) {
  try {
    const { FileBox } = await import('file-box')
    const fileBox = FileBox.fromUrl('https://workpro.s3.cn-northwest-1.amazonaws.com.cn/link_msg/f28e011f-18af-49a3-acf4-d55043a2b47c/API%E7%BD%91%E5%85%B3.png', {
      name: 'API网关.png'
    })
    await message.say(fileBox)
  } catch (error) {
    console.error('处理图像消息时发生错误:', error)
    await message.say('抱歉，无法发送该图像')
  }
}

async function handleAttachmentSend(message: Message) {
  try {
    const { FileBox } = await import('file-box')
    const fileBox = FileBox.fromUrl('https://workpro.s3.cn-northwest-1.amazonaws.com.cn/link_msg/69becc03-dcda-4f65-861b-adef263d809f/%E7%8F%AD%E7%BA%A7%E7%BB%9F%E8%AE%A1.xlsx', {
      name: '班级统计.xlsx'
    })
    await message.say(fileBox)
  } catch (error) {
    console.error('处理附件消息时发生错误:', error)
    await message.say('抱歉，无法发送该附件')
  }
}

async function handleUrlLinkSend(message: Message) {
  try {
    const { WechatyBuilder } = await import('@juzi/wechaty')
    const bot = WechatyBuilder.build({})
    const urlLink = new bot.UrlLink({
      description: '消息相关接口统计数量',
      thumbnailUrl: 'https://lowcode-0grf6ofp6211b744-1312402421.tcloudbaseapp.com/resources/2024-08/lowcode-1928640',
      title: '数据统计',
      url: 'https://lowcode-0grf6ofp6211b744-1312402421.tcloudbaseapp.com/app-8ck1g7qf/production/index'
    })
    await message.say(urlLink)
  } catch (error) {
    console.error('处理链接消息时发生错误:', error)
    await message.say('抱歉，无法发送该链接')
  }
}

async function handleMiniProgramSend(message: Message) {
  try {
    const { WechatyBuilder } = await import('@juzi/wechaty')
    const bot = WechatyBuilder.build({})
    const miniProgram = new bot.MiniProgram({
      appid: 'wx6f455216a514f31f',
      title: '我正在使用Authing认证身份，你也来试试吧',
      pagePath: 'pages/index/index.whml',
      thumbUrl:
        '30590201000452305002010002041092541302033d0af802040b30feb602045df0c2c5042b777875706c6f61645f31373533353339353230344063686174726f6f6d3131355f313537363035393538390204010400030201000400',
      thumbKey: '42f8609e62817ae45cf7d8fefb532e83'
    })
    await message.say(miniProgram)
  } catch (error) {
    console.error('处理小程序消息时发生错误:', error)
    await message.say('抱歉，无法发送该小程序')
  }
}

async function handleLocationSend(message: Message) {
  try {
    const { WechatyBuilder } = await import('@juzi/wechaty')
    const bot = WechatyBuilder.build({})
    const location = new bot.Location({
      latitude: 31.2833,
      longitude: 121.5041,
      name: '同济大学(四平路校区)-图书馆',
      description: '同济大学(四平路校区)-图书馆的地理位置'
    })
    await message.say(location)
  } catch (error) {
    console.error('处理位置消息时发生错误:', error)
    await message.say('抱歉，无法发送该位置')
  }
}
