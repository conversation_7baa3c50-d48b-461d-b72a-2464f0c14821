import { insertConversationRecord } from '../dao/conversationDao.js';
export async function logUserMessage(message) {
    const room = message.room();
    const talker = message.talker();
    const text = message.text();
    const rec = {
        conversation_id: room ? room.id : talker.id,
        conversation_type: (room ? 'group' : 'private'),
        room_id: room ? room.id : null,
        room_name: room ? await room.topic() : null,
        user_id: talker.id,
        user_name: talker.name(),
        role: 'user',
        message_type: 'text',
        content: text,
        content_url: null,
        metadata: null,
        model: null,
        status_code: null,
        duration_ms: null,
        parent_message_id: null,
        trace_id: null,
        ip_address: null,
        device_info: null
    };
    await insertConversationRecord(rec);
}
export async function logAssistantText(room, talker, content, model) {
    const rec = {
        conversation_id: room ? room.id : talker.id,
        conversation_type: (room ? 'group' : 'private'),
        room_id: room ? room.id : null,
        room_name: room ? await room.topic() : null,
        user_id: talker.id,
        user_name: talker.name(),
        role: 'assistant',
        message_type: 'text',
        content,
        content_url: null,
        metadata: null,
        model: model || null,
        status_code: 200,
        duration_ms: null,
        parent_message_id: null,
        trace_id: null,
        ip_address: null,
        device_info: null
    };
    await insertConversationRecord(rec);
}
//# sourceMappingURL=conversationLog.js.map