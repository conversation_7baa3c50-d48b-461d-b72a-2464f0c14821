# 同济同学企业微信机器人 - 管理员开发说明书

## 项目概述

同济同学企业微信机器人是基于 Wechaty 框架开发的企业微信智能对话机器人，采用 TypeScript 开发，支持多智能体架构、持久化存储、权限管理等企业级功能。

## 系统架构

### 技术栈

- **框架**: Wechaty (企业微信集成)
- **语言**: TypeScript/Node.js
- **数据库**: MySQL (主数据存储) + Redis (缓存和会话管理)
- **容器化**: Docker
- **流式处理**: Go 服务 (可选)

### 核心模块

```
src/
├── agents/           # 智能体模块
├── config/          # 配置管理
├── dao/             # 数据访问层
├── handlers/        # 消息处理器
├── services/        # 业务服务层
└── utils/           # 工具函数
```

## 环境配置

### 必需环境变量

```bash
# Wechaty配置
WECHATY_TOKEN=puppet_workpro_xxxxx
WECHATY_PUPPET=@juzi/wechaty-puppet-service

# MySQL配置
MYSQL_HOST=YOUR_HOST
MYSQL_PORT=3306
MYSQL_USER=YOUR_USER
MYSQL_PASSWORD=YOUR_PASSWORD
MYSQL_DB=YOUR_DB
MYSQL_POOL=10

# Redis配置
REDIS_HOST=YOUR_HOST
REDIS_PORT=6379
REDIS_PASSWORD=YOUR_PASSWORD
REDIS_DB=0

# Docker环境
IS_DOCKER=true
VERIFY_CODE=verification_code
```

### 数据库初始化

```sql
-- 智能体配置表
CREATE TABLE `agent`  (
  `appid` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '智能体appid',
  `id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '智能体id',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '智能体名称',
  `appkey` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '智能体访问密钥',
  `base_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '智能体调用的基础URL',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '描述信息',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`appid`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '智能体表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of agent
-- ----------------------------
INSERT INTO `agent` VALUES ('d24qen3m1orcf178pn4g', 'info', '信息办助手', 'd319dblnmn9rlsg0oibg', 'https://agent.tongji.edu.cn/api/proxy/api/v1', '信息办助手', '2025-09-15 16:23:43', '2025-09-15 16:23:43');
INSERT INTO `agent` VALUES ('tjtx', 'tjtx', '同济同学', 'cvvgdkuh36688d49d5fg', 'https://agent.tongji.edu.cn/api/proxy/api/v1', '同济同学', '2025-09-15 16:22:40', '2025-09-15 16:57:57');

SET FOREIGN_KEY_CHECKS = 1;

-- 群聊配置表
CREATE TABLE `group_config`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `room_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '群聊ID',
  `room_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '群聊名称',
  `agent_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '默认绑定的智能体ID',
  `allow_agent_switch` tinyint(1) NULL DEFAULT 0 COMMENT '是否允许切换智能体（0=否，1=是）',
  `enable_push` tinyint(1) NULL DEFAULT 1 COMMENT '是否开启推送（0=否，1=是）',
  `enable_blacklist` tinyint(1) NULL DEFAULT 0 COMMENT '是否开启黑名单（0=否，1=是）',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uniq_room_id`(`room_id` ASC) USING BTREE,
  INDEX `idx_agent`(`agent_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 11 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '群聊配置表' ROW_FORMAT = Dynamic;

SET FOREIGN_KEY_CHECKS = 1;


-- 对话记录表
CREATE TABLE `conversation_record`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `conversation_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '会话ID（群聊或私聊唯一标识）',
  `conversation_type` enum('private','group') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT 'private' COMMENT '会话类型：private=私聊，group=群聊',
  `room_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '群聊ID（仅群聊有值）',
  `room_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '群聊名称（仅群聊有值）',
  `user_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '发送者用户ID',
  `user_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '发送者昵称',
  `role` enum('user','assistant','system') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '消息角色：用户/AI/系统',
  `message_type` enum('text','image','audio','video','file','event') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT 'text' COMMENT '消息类型',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '消息内容（文本消息）',
  `content_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '多媒体资源地址（图片/音频/文件等）',
  `metadata` json NULL COMMENT '扩展信息（AI参数、tokens、模型配置、上下文摘要等）',
  `model` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '使用的模型',
  `status_code` int NULL DEFAULT NULL COMMENT '请求状态码',
  `duration_ms` int NULL DEFAULT NULL COMMENT '耗时（毫秒）',
  `parent_message_id` bigint NULL DEFAULT NULL COMMENT '父消息ID（引用/回复/上下文追踪）',
  `trace_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '调用链追踪ID',
  `ip_address` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '用户IP（IPv4/IPv6）',
  `device_info` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '设备信息（浏览器UA/客户端等）',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '消息时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_conv_time`(`conversation_id` ASC, `created_at` ASC) USING BTREE,
  INDEX `idx_user_time`(`user_id` ASC, `created_at` ASC) USING BTREE,
  INDEX `idx_room_time`(`room_id` ASC, `created_at` ASC) USING BTREE,
  FULLTEXT INDEX `ft_content`(`content`)
) ENGINE = InnoDB AUTO_INCREMENT = 185 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '会话记录表' ROW_FORMAT = Dynamic;
```

## 智能体管理

### 智能体架构

每个智能体包含以下文件：

```
src/agents/{agent_name}/
├── config.ts    # 智能体配置
├── agent.ts     # 智能体实现
└── api.ts       # API调用逻辑
```

### 添加新智能体

1. **创建配置文件** (`config.ts`)

```typescript
export const NEW_AGENT_CONFIG = {
  id: 'new_agent',
  name: '新智能体',
  kind: 'api-type' as const,
  description: '智能体描述',
  api: {
    baseUrl: 'https://api.example.com',
    apiKey: 'your_api_key',
    userId: 'user_id'
  }
}
```

2. **实现智能体** (`agent.ts`)

```typescript
import { Agent } from '../types.js'
import { NEW_AGENT_CONFIG } from './config.js'

export const newAgent: Agent = {
  id: NEW_AGENT_CONFIG.id,
  name: NEW_AGENT_CONFIG.name,
  kind: NEW_AGENT_CONFIG.kind,
  description: NEW_AGENT_CONFIG.description,
  respond: async (query, ctx) => {
    // 实现对话逻辑
    const { room, talker, message } = ctx
    // 调用API并返回结果
    return 'AI回复内容'
  }
}
```

3. **实现 API 调用** (`api.ts`)

```typescript
export async function createConversation(sessionKey: string): Promise<string | null> {
  // 创建会话逻辑
}

export async function* chatQueryStreaming(conversationId: string, query: string) {
  // 流式对话逻辑
}
```

4. **注册智能体** 在数据库中添加智能体记录：

```sql
INSERT INTO agents (id, name, description, base_url, appid, appkey)
VALUES ('new_agent', '新智能体', '描述', 'https://api.example.com', 'app_id', 'api_key');
```

### 智能体配置管理

智能体配置支持数据库动态配置，配置变更实时生效：

**Redis 存储结构**：

- `agent:config:{agentId}` - 智能体配置缓存
- `agent:setting:{sessionKey}` - 会话智能体设置

## 消息处理流程

### 消息处理架构

```
消息接收 → 类型判断 → 权限检查 → 命令解析 → 智能体路由 → 响应生成
```

### 消息类型处理

1. **文本消息**

   - 命令解析 (`parseAgentCommand`)
   - 关键词检查 (`checkAndHandleKeyword`)
   - AI 对话处理 (`handleAIResponse`)

2. **多媒体消息**
   - 图片：`handleGroupImage` / `handlePrivateImage`
   - 语音：`handleGroupAudio` / `handlePrivateAudio`
   - 视频：`handleGroupVideo` / `handlePrivateVideo`
   - 文件：`handleGroupFile` / `handlePrivateFile`

### 权限管理

```typescript
// 检查智能体命令权限
const hasPermission = await PermissionManager.canExecuteAgentCommand(room, talker)

// 权限级别
- 群聊：群主/管理员可执行智能体命令
- 私聊：默认拥有所有权限
```

## 数据持久化

### Redis 缓存策略

```typescript
// 存储结构
{
  "agent:setting:{sessionKey}": "agentId",           // 智能体设置
  "room:info:{roomId}": "roomInfo",                  // 群聊信息
  "agent:config:{agentId}": "agentConfig",           // 智能体配置
  "user:info:{pid}": "userInfo",                     // 用户信息缓存
  "conversation:{sessionKey}": "conversationId"      // 会话ID
}
```

### 缓存过期策略

- 用户信息：24 小时
- 群聊信息：实时更新
- 智能体设置：永久保存
- 会话信息：根据活跃度动态管理

## 学工信息查询

### API 集成

```typescript
// 同济学工API
const API_URL = 'https://api.tongji.edu.cn/v2/dc/user/person_info_by_pid'

// 支持的查询关键词
const KEYWORDS = ['学号', '学工号', '工号', '手机号', '部门', '学院', '姓名']
```

### 查询流程

1. 关键词检测
2. PID 提取（从消息文本或用户信息）
3. API 调用
4. 结果缓存
5. 格式化输出

## 会话管理

### 会话状态管理

```typescript
// 会话状态
interface ConversationState {
  isActive: boolean
  lastActivity: number
  sessionKey: string
}

// 状态操作
activateConversation(sessionKey) // 激活会话
endConversation(sessionKey) // 结束会话
isConversationActive(sessionKey) // 检查会话状态
updateLastActivity(sessionKey) // 更新活动时间
```

### 连续对话逻辑

- 群聊：@机器人后激活会话，支持短时间内连续对话
- 私聊：默认支持连续对话
- 超时机制：会话超时自动结束

## 部署指南

### Docker 部署

```bash
# 构建镜像
docker build -t tongji-bot .

# 运行容器
docker run -d \
  --name tongji-bot \
  -e MYSQL_HOST=your_mysql_host \
  -e REDIS_HOST=your_redis_host \
  tongji-bot
```

### 环境部署

```bash
# 安装依赖
npm install

# 编译TypeScript
npm run build

# 启动服务
npm start
```

### Go 流式服务部署

```bash
cd go/
docker build -t tongji-go-service .
docker run -d -p 8084:8084 tongji-go-service
```

## 监控与日志

### 日志记录

- 用户消息记录：`logUserMessage`
- 机器人回复记录：`logAssistantText`
- 多媒体消息记录：`logMultimediaMessage`

### 性能监控

- API 响应时间
- 数据库连接池状态
- Redis 连接状态
- 内存使用情况
