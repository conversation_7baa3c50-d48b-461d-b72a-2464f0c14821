import { execute, query } from '../utils/db.js';
export async function insertConversationRecord(rec) {
    await execute(`INSERT INTO conversation_record (
      conversation_id, conversation_type, room_id, room_name,
      user_id, user_name, role,
      message_type, content, content_url, metadata,
      model, status_code, duration_ms,
      parent_message_id, trace_id,
      ip_address, device_info
    ) VALUES (
      :conversation_id, :conversation_type, :room_id, :room_name,
      :user_id, :user_name, :role,
      :message_type, :content, :content_url, CAST(:metadata AS JSON),
      :model, :status_code, :duration_ms,
      :parent_message_id, :trace_id,
      :ip_address, :device_info
    )`, rec);
}
export async function listConversationRecords(conversationId, limit = 50) {
    return query('SELECT * FROM conversation_record WHERE conversation_id = :conversationId ORDER BY created_at DESC LIMIT :limit', { conversationId, limit });
}
//# sourceMappingURL=conversationDao.js.map