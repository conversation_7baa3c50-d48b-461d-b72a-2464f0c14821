import { getGroupConfig } from '../../dao/groupConfigDao.js';
// 工具：统一会话键（群用 room.id，私聊用 talker.id）
export function getSessionKey(room, talker) {
    return room ? room.id : talker.id;
}
// 获取当前智能体ID：群聊从group_config读取；私聊返回默认'tjtx'
export async function getCurrentAgentId(sessionKey, room) {
    if (room) {
        const cfg = await getGroupConfig(room.id);
        return cfg && cfg.agent_id ? cfg.agent_id : 'tjtx';
    }
    return 'tjtx';
}
// 设置当前智能体ID
export async function setCurrentAgentId(sessionKey, agentId, _agentRegistry, room) {
    if (!room)
        return false;
    const { upsertGroupConfig } = await import('../../dao/groupConfigDao.js');
    await upsertGroupConfig({ room_id: room.id, agent_id: agentId });
    return true;
}
//# sourceMappingURL=session.js.map