{"version": 3, "file": "conversationState.js", "sourceRoot": "", "sources": ["../../../src/utils/conversationState.ts"], "names": [], "mappings": "AAOA,YAAY;AACZ,MAAM,kBAAkB,GAAG,IAAI,GAAG,EAA6B,CAAA;AAE/D,WAAW;AACX,MAAM,oBAAoB,GAAG,CAAC,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC,CAAA;AAErG,iBAAiB;AACjB,MAAM,UAAU,yBAAyB,CAAC,IAAY;IACpD,MAAM,SAAS,GAAG,IAAI,CAAC,WAAW,EAAE,CAAA;IACpC,OAAO,oBAAoB,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,EAAE,CAAC,SAAS,CAAC,QAAQ,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC,CAAC,CAAA;AAC1F,CAAC;AAED,gBAAgB;AAChB,MAAM,UAAU,iBAAiB,CAAC,IAAY,EAAE,WAAqB;IACnE,MAAM,cAAc,GAAG,aAAa,CAAA;IACpC,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,CAAA;IAE3C,IAAI,QAAQ,EAAE,CAAC;QACb,OAAO,QAAQ,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,EAAE;YAC/B,MAAM,aAAa,GAAG,OAAO,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,IAAI,EAAE,CAAA;YACrD,OAAO,WAAW,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,QAAQ,CAAC,aAAa,CAAC,IAAI,aAAa,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAA;QACvG,CAAC,CAAC,CAAA;IACJ,CAAC;IAED,OAAO,KAAK,CAAA;AACd,CAAC;AAED,YAAY;AACZ,MAAM,UAAU,oBAAoB,CAAC,UAAkB;IACrD,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,UAAU,CAAC,EAAE,CAAC;QACxC,kBAAkB,CAAC,GAAG,CAAC,UAAU,EAAE;YACjC,QAAQ,EAAE,KAAK;YACf,YAAY,EAAE,IAAI,IAAI,EAAE;YACxB,WAAW,EAAE,CAAC,GAAG,oBAAoB,CAAC;SACvC,CAAC,CAAA;IACJ,CAAC;IACD,OAAO,kBAAkB,CAAC,GAAG,CAAC,UAAU,CAAE,CAAA;AAC5C,CAAC;AAED,OAAO;AACP,MAAM,UAAU,oBAAoB,CAAC,UAAkB;IACrD,MAAM,KAAK,GAAG,oBAAoB,CAAC,UAAU,CAAC,CAAA;IAC9C,KAAK,CAAC,QAAQ,GAAG,IAAI,CAAA;IACrB,KAAK,CAAC,YAAY,GAAG,IAAI,IAAI,EAAE,CAAA;AACjC,CAAC;AAED,OAAO;AACP,MAAM,UAAU,eAAe,CAAC,UAAkB;IAChD,MAAM,KAAK,GAAG,oBAAoB,CAAC,UAAU,CAAC,CAAA;IAC9C,KAAK,CAAC,QAAQ,GAAG,KAAK,CAAA;IACtB,KAAK,CAAC,YAAY,GAAG,IAAI,IAAI,EAAE,CAAA;AACjC,CAAC;AAED,uBAAuB;AACvB,MAAM,UAAU,oBAAoB,CAAC,UAAkB;IACrD,MAAM,KAAK,GAAG,oBAAoB,CAAC,UAAU,CAAC,CAAA;IAC9C,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAA;IACtB,MAAM,QAAQ,GAAG,GAAG,CAAC,OAAO,EAAE,GAAG,KAAK,CAAC,YAAY,CAAC,OAAO,EAAE,CAAA;IAE7D,OAAO,KAAK,CAAC,QAAQ,IAAI,QAAQ,GAAG,CAAC,GAAG,EAAE,GAAG,IAAI,CAAA,CAAC,MAAM;AAC1D,CAAC;AAED,WAAW;AACX,MAAM,UAAU,kBAAkB,CAAC,UAAkB;IACnD,MAAM,KAAK,GAAG,oBAAoB,CAAC,UAAU,CAAC,CAAA;IAC9C,KAAK,CAAC,YAAY,GAAG,IAAI,IAAI,EAAE,CAAA;AACjC,CAAC;AAED,mBAAmB;AACnB,MAAM,UAAU,oBAAoB;IAClC,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAA;IACtB,MAAM,OAAO,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAA;IAE9B,KAAK,MAAM,CAAC,UAAU,EAAE,KAAK,CAAC,IAAI,kBAAkB,CAAC,OAAO,EAAE,EAAE,CAAC;QAC/D,MAAM,QAAQ,GAAG,GAAG,CAAC,OAAO,EAAE,GAAG,KAAK,CAAC,YAAY,CAAC,OAAO,EAAE,CAAA;QAC7D,IAAI,QAAQ,GAAG,OAAO,EAAE,CAAC;YACvB,kBAAkB,CAAC,MAAM,CAAC,UAAU,CAAC,CAAA;QACvC,CAAC;IACH,CAAC;AACH,CAAC;AAED,sBAAsB;AACtB,WAAW,CAAC,oBAAoB,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAA"}