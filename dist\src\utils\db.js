import mysql from 'mysql2/promise';
import { GL<PERSON><PERSON>L_CONFIG } from '../config/index.js';
export const pool = mysql.createPool({
    host: GLOBAL_CONFIG.mysql.host,
    port: GLOBAL_CONFIG.mysql.port,
    user: GL<PERSON><PERSON>L_CONFIG.mysql.user,
    password: GLOBAL_CONFIG.mysql.password,
    database: GLOBAL_CONFIG.mysql.database,
    waitForConnections: true,
    connectionLimit: GLOBAL_CONFIG.mysql.connectionLimit,
    queueLimit: 0,
    namedPlaceholders: true
});
export async function query(sql, params) {
    try {
        const [rows] = await pool.query(sql, params);
        return rows;
    }
    catch (e) {
        console.error('MySQL query error:', e?.message, { sql, params });
        throw e;
    }
}
export async function execute(sql, params) {
    await pool.execute(sql, params);
}
//# sourceMappingURL=db.js.map