// 智能体命令解析
export function parseAgentCommand(text) {
    const trimmed = text.trim();
    const stripBrackets = (s) => s.replace(/[<>()（）【】\[\]>]/g, '').trim();
    // /agent set <id>
    if (/^\/agent\s+set\s+/i.test(trimmed)) {
        const raw = trimmed.replace(/^\/agent\s+set\s+/i, '');
        return { cmd: 'set', arg: stripBrackets(raw) };
    }
    // 中文：设置智能体 / 切换智能体 [可选冒号] [可选空格] [可选括号]
    const setMatch = trimmed.match(/^(设置智能体|切换智能体)[:：]?\s*(.+)$/);
    if (setMatch) {
        return { cmd: 'set', arg: stripBrackets(setMatch[2]) };
    }
    if (/^\/agent\s+list$/i.test(trimmed) || /^(列出智能体|智能体列表)$/i.test(trimmed)) {
        return { cmd: 'list' };
    }
    if (/^\/agent\s+current$/i.test(trimmed) || /^(当前智能体)$/i.test(trimmed)) {
        return { cmd: 'current' };
    }
    return { cmd: null };
}
//# sourceMappingURL=commands.js.map