import axios from 'axios'
import https from 'https'
import { GLOBAL_CONFIG } from '../config/index.js'

// OAuth2 token响应类型
interface OAuth2TokenResponse {
  access_token: string
  expires_in: number
  not_before_policy: number
  refresh_expires_in: number
  scope: string
  token_type: string
}

// Token缓存
interface TokenCache {
  token: string
  expiresAt: number
}

// 同济学工信息API响应类型
export interface TongjiUserInfo {
  deptCode: string
  deptName: string
  name: string
  pid: string
  statusCode: string
  statusName: string
  userId: string
  userTypeCode: string
  userTypeName: string
}

export interface TongjiApiResponse {
  code: string
  data: {
    count: number
    userInfos?: TongjiUserInfo[] // 保持兼容性
    list?: TongjiUserInfo[] // 实际API返回的字段
    sincePid?: string
  }
  msg: string
}

// 学工信息查询关键字
export const TONGJI_KEYWORDS = ['学号', '学工号', '工号', '手机号', '部门', '学院', '姓名']

// 同济API服务类
export class TongjiApiService {
  private baseUrl: string
  private timeout: number
  private tokenCache: TokenCache | null = null

  constructor() {
    this.baseUrl = GLOBAL_CONFIG.tongjiApi.USER_PERSON_INFO_BY_PID_URL
    this.timeout = GLOBAL_CONFIG.tongjiApi.timeout
  }

  // 获取OAuth2 access token
  private async getAccessToken(): Promise<string | null> {
    // 检查缓存的token是否还有效
    if (this.tokenCache && Date.now() < this.tokenCache.expiresAt) {
      return this.tokenCache.token
    }

    try {
      const config = {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        },
        timeout: this.timeout,
        // 忽略SSL证书验证（仅用于测试环境）
        httpsAgent: new https.Agent({
          rejectUnauthorized: false
        })
      }

      const response = await axios.post<OAuth2TokenResponse>(
        GLOBAL_CONFIG.tongjiApi.OAUTH2_TOKEN_URL,
        new URLSearchParams({
          client_id: GLOBAL_CONFIG.tongjiApi.CLIENT_ID,
          client_secret: GLOBAL_CONFIG.tongjiApi.CLIENT_SECRET,
          grant_type: 'client_credentials'
        }),
        config
      )

      if (response.data.access_token) {
        // 缓存token，提前5分钟过期以确保安全
        const expiresAt = Date.now() + (response.data.expires_in - 300) * 1000
        this.tokenCache = {
          token: response.data.access_token,
          expiresAt
        }
        console.log('OAuth2 token获取成功')
        return response.data.access_token
      }
    } catch (error) {
      console.error('获取OAuth2 token失败:', error)
    }

    return null
  }

  // 根据PID查询学工信息
  async getPersonInfoByPid(pid: string): Promise<TongjiUserInfo[] | null> {
    try {
      // 获取access token
      const token = await this.getAccessToken()
      if (!token) {
        console.error('无法获取access token')
        return null
      }

      const config = {
        params: {
          pid,
          sincePid: '',
          sinceUpdateTime: ''
        },
        headers: {
          Authorization: `Bearer ${token}`
        },
        timeout: this.timeout,
        // 忽略SSL证书验证（仅用于测试环境）
        httpsAgent: new https.Agent({
          rejectUnauthorized: false
        })
      }

      const response = await axios.get<TongjiApiResponse>(this.baseUrl, config)

      console.log('API响应状态:', response.status)
      console.log('API响应数据:', JSON.stringify(response.data, null, 2))

      if (response.data.code === 'A00000') {
        if (response.data.data) {
          // 尝试从 userInfos 或 list 字段获取数据
          const userInfos = response.data.data.userInfos || response.data.data.list
          if (userInfos && userInfos.length > 0) {
            console.log(`查询到 ${userInfos.length} 条学工信息记录`)
            return userInfos
          } else {
            console.log('API返回成功但无用户信息数据')
            return null
          }
        } else {
          console.log('API返回成功但无data字段')
          return null
        }
      } else {
        console.error('API返回错误:', response.data.msg)
        return null
      }
    } catch (error) {
      console.error('查询学工信息失败:', error)
      return null
    }
  }

  formatUserInfo(userInfos: TongjiUserInfo[]): string {
    if (!userInfos || userInfos.length === 0) {
      return '未找到相关学工信息'
    }

    let result = `学工信息查询结果（共${userInfos.length}条记录）：\n\n`

    userInfos.forEach((user, index) => {
      result += `【记录 ${index + 1}】\n`
      result += `姓名：${user.name}\n`
      result += `学工号：${user.pid}\n`
      result += `用户ID：${user.userId}\n`
      result += `部门：${user.deptName} (${user.deptCode})\n`
      result += `用户类型：${user.userTypeName}\n`
      result += `状态：${user.statusName}\n`
      if (index < userInfos.length - 1) {
        result += '\n---\n\n'
      }
    })

    return result
  }

  // 检查文本是否包含学工信息查询关键字
  containsTongjiKeywords(text: string): boolean {
    return TONGJI_KEYWORDS.some((keyword) => text.includes(keyword))
  }

  // 从文本中取PID
  extractPidFromText(text: string): string[] {
    const pidPattern = /\b\d{8,}\b/g
    const matches = text.match(pidPattern)
    return matches || []
  }

  // 从talker中取PID
  extractPidFromTalker(talker: any): string[] {
    const pids: string[] = []

    try {
      // talker的payload中的handle和wexin字段
      if (talker.payload) {
        const payload = talker.payload
        if (payload.handle && typeof payload.handle === 'string') {
          pids.push(payload.handle)
        }
        if (payload.wexin && typeof payload.wexin === 'string') {
          pids.push(payload.wexin)
        }
      }
    } catch (error) {
      console.error('从talker提取PID失败:', error)
    }

    return pids
  }

  // 处理学工信息查询
  async handleTongjiQuery(text: string, talker?: any): Promise<string | null> {
    if (!this.containsTongjiKeywords(text)) {
      return null
    }

    let pids = this.extractPidFromText(text)

    if (pids.length === 0 && talker) {
      pids = this.extractPidFromTalker(talker)
    }

    if (pids.length === 0) {
      return '未找到有效的学工号'
    }

    let result = ''
    for (const pid of pids) {
      const userInfos = await this.getPersonInfoByPid(pid)
      if (userInfos) {
        result += this.formatUserInfo(userInfos)
        if (pids.length > 1) {
          result += '\n\n'
        }
      } else {
        result += `查询学工号基本信息失败\n\n`
      }
    }

    return result.trim()
  }
}

export const tongjiApiService = new TongjiApiService()
